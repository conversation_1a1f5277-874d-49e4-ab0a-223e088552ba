use std::sync::Arc;
use std::time::Duration;

use crate::port_forward::route_table::RouteTable;
use crate::tunnel::tunnel::Tunnel;
use flyshadow_common::tunnel::tunnel_package::{PackageCmd, PackageProtocol, TunnelPackage};
use socket2::SockRef;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::{tcp::OwnedWriteHalf, TcpStream, UdpSocket};
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tokio::time::Instant;

/// 表示一个网络路由，支持 TCP 或 UDP 协议。
pub struct Route {
    pub protocol: PackageProtocol, // 协议类型（TCP/UDP）
    pub source_addr: String,       // 源地址
    pub target_addr: String,       // 目标地址
    pub tunnel: RwLock<Option<Arc<Tunnel>>>,  // 隧道
    pub route_table: Arc<RouteTable>,         // 路由表
    pub tcp_read_job: RwLock<Option<JoinHandle<()>>>, // TCP 读取任务
    pub tcp_write_job: Arc<RwLock<Option<JoinHandle<()>>>>, // TCP 写入任务
    pub tcp_writer: RwLock<Option<OwnedWriteHalf>>, // TCP 写入器
    pub udp_read_job: Option<JoinHandle<()>>, // UDP 读取任务
    pub udp_socket: RwLock<Option<Arc<UdpSocket>>>, // UDP 套接字
    pub active_time: Arc<RwLock<Instant>>,    // 活跃时间
    pub uuid: String,                         // 唯一标识符
}

impl Route {
    /// 发送 TCP 数据到目标地址。
    pub async fn send_tcp_data_to_target(&self, mut data: Vec<u8>) {
        if self.protocol == PackageProtocol::TCP {
            if let Some(tcp_writer) = self.tcp_writer.write().await.as_mut() {
                if data.len() < 32 {
                    // 数据长度不足，关闭连接
                    self.route_table
                        .remove_route(&self.source_addr, &self.target_addr, PackageProtocol::TCP)
                        .await;
                    return;
                }
                data.drain(..32); // 移除前32字节
                if let Err(_) = tcp_writer.write_all(data.as_slice()).await {
                    // 写入失败，关闭连接
                    self.route_table
                        .remove_route(&self.source_addr, &self.target_addr, PackageProtocol::TCP)
                        .await;
                }
            }
        }
    }

    /// 发送 UDP 数据到目标地址。
    pub async fn send_udp_data_to_target(&self, target_addr: String, mut data: Vec<u8>) {
        if self.protocol == PackageProtocol::UDP {
            if let Some(udp_socket) = self.udp_socket.read().await.as_ref() {
                if data.len() < 32 {
                    return;
                }
                data.drain(..32);

                *self.active_time.write().await = Instant::now(); // 更新活跃时间
                let _ = udp_socket.send_to(data.as_slice(), target_addr).await; // 发送 UDP 数据
            }
        }
    }

    /// 连接到目标地址并处理 TCP 通信。
    pub async fn connect(self: Arc<Self>) {
        let route_table = self.route_table.clone();
        let target_addr = self.target_addr.clone();
        let source_addr = self.source_addr.clone();
        let protocol = self.protocol;

        let route = self.clone();

        // 启动一个异步任务来处理 TCP 通信。
        let _ = self.tcp_read_job.write().await.insert(spawn(async move {
            match TcpStream::connect(target_addr.clone()).await {
                Ok(server_stream) => {
                    route.start_tcp_transmit(server_stream).await;
                }
                Err(_) => {
                    route_table
                        .remove_route(&source_addr, &target_addr, protocol)
                        .await;
                }
            }
        }));
    }

    /// 启动tcp流量转发
    pub async fn start_tcp_transmit(&self, server_stream: TcpStream) {
        let route_table = self.route_table.clone();
        let target_addr = self.target_addr.clone();
        let source_addr = self.source_addr.clone();
        let protocol = self.protocol.clone();
        let tunnel = self.tunnel.read().await.clone();
        let uuid = self.uuid.clone();

        let _ = server_stream.set_nodelay(true);
        let sock_ref = SockRef::from(&server_stream);
        let _ = sock_ref.set_keepalive(true);

        let (mut server_reader, server_writer) = server_stream.into_split();

        // 存储TCP写入器供直接调用使用
        *self.tcp_writer.write().await = Some(server_writer);

        // 循环读取 TCP 流中的数据。
        let uuid_byte = uuid.as_bytes();
        let mut server_buffer = [0u8; 16384];

        loop {
            match server_reader.read(&mut server_buffer).await {
                Ok(0) => {
                    break;
                }
                Ok(n) => {
                    let mut data = vec![];
                    data.append(&mut uuid_byte.to_vec());
                    data.append(&mut server_buffer[..n].to_vec());
                    let tunnel_package = TunnelPackage::new(
                        PackageCmd::CTdata,
                        PackageProtocol::TCP,
                        Some(source_addr.clone()),
                        Some(target_addr.clone()),
                        Some(data),
                    );
                    if let Some(tunnel) = tunnel.as_ref() {
                        if tunnel.write_to_tunnel(tunnel_package).await.is_err() {
                            break;
                        }
                    }
                }
                Err(_) => {
                    break;
                }
            }
        }
        route_table
            .remove_route(&source_addr, &target_addr, protocol)
            .await;
    }

    /// 断开路由连接，并通知服务器处理。
    pub async fn disconnect(&self) {
        let tunnel_package = TunnelPackage::new(
            PackageCmd::CCloseConnect,
            PackageProtocol::TCP,
            Some(self.source_addr.clone()),
            Some(self.target_addr.clone()),
            Some(self.uuid.as_bytes().to_vec()),
        );
        if let Some(tunnel) = self.tunnel.read().await.as_ref() {
            let _ = tunnel.write_to_tunnel(tunnel_package).await;
        }
        self.close().await;
    }

    /// 关闭路由，终止任何活跃的任务并清理资源。
    pub async fn close(&self) {
        *self.tunnel.write().await = None; // 清空隧道
        if let Some(_) = self.udp_socket.write().await.take() {}; // 释放 UDP 套接字
        if let Some(_) = self.tcp_writer.write().await.take() {}; // 释放 TCP 写入器
        if let Some(udp_read_job) = self.udp_read_job.as_ref() {
            udp_read_job.abort(); // 取消 UDP 读取任务
        }
        if let Some(tcp_write_job) = self.tcp_write_job.write().await.take() {
            tcp_write_job.abort(); // 取消 TCP 写入任务
        }
        if let Some(tcp_read_job) = self.tcp_read_job.write().await.take() {
            tcp_read_job.abort(); // 取消 TCP 读取任务
        }
    }

    /// 检查路由是否超时，如果超时则认为它已经无效。
    pub async fn is_timeout(&self) -> bool {
        let now = Instant::now();
        // UDP 超时时间为 5 分钟。
        if self.protocol == PackageProtocol::UDP {
            now.duration_since(*self.active_time.read().await) > Duration::from_secs(60 * 5)
        } else {
            // TCP 超时时间为 2 小时。
            now.duration_since(*self.active_time.read().await) > Duration::from_secs(60 * 60 * 2)
        }
    }
}
