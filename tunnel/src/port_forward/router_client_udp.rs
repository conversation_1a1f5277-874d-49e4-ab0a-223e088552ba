use crate::port_forward::route_table::RouteTable;
use crate::port_forward::router::Route;
use crate::tunnel::tunnel::Tunnel;
use flyshadow_common::tunnel::tunnel_package::PackageProtocol;
use std::sync::Arc;
use tokio::net::UdpSocket;
use tokio::sync::RwLock;
use tokio::time::Instant;

impl Route {
    /// 创建一个新的路由，指定协议、源地址、目标地址、隧道和路由表。
    pub async fn new_client_udp(
        udp_socket: Arc<UdpSocket>,
        target_addr: String,
        source_addr: String,
        tunnel: Arc<Tunnel>,
        route_table: Arc<RouteTable>,
        uuid: String,
    ) -> Arc<Route> {
        // 初始化活跃时间和其他路由参数。
        let active_time = Arc::new(RwLock::new(Instant::now()));

        Arc::new(Route {
            protocol: PackageProtocol::UDP,
            source_addr,
            target_addr,
            tunnel: RwLock::new(Some(tunnel)),
            route_table,
            tcp_read_job: RwLock::new(None),
            tcp_write_job: Arc::new(RwLock::new(None)),
            tcp_writer: RwLock::new(None),
            udp_read_job: None,
            udp_socket: RwLock::new(Some(udp_socket)),
            active_time,
            uuid,
        })
    }
}