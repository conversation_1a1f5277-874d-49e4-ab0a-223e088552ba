use crate::port_forward::route_table::RouteTable;
use crate::port_forward::router::Route;
use crate::tunnel::tunnel::Tunnel;
use flyshadow_common::tunnel::tunnel_package::{PackageCmd, PackageProtocol, TunnelPackage};
use std::sync::Arc;
use tokio::net::UdpSocket;
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::time::Instant;

impl Route {
    /// 创建一个新的路由，指定协议、源地址、目标地址、隧道和路由表。
    pub async fn new_server(
        protocol: PackageProtocol,
        target_addr: String,
        source_addr: String,
        tunnel: Arc<Tunnel>,
        route_table: Arc<RouteTable>,
        uuid: String,
    ) -> Arc<Route> {
        // 初始化活跃时间和其他路由参数。
        let active_time = Arc::new(RwLock::new(Instant::now()));
        let active_time_clone = active_time.clone();
        let source_addr_clone = source_addr.clone();
        let tunnel_clone = tunnel.clone();
        let uuid_clone = uuid.clone();
        let mut udp_read_job_opt = None;
        let mut udp_socket_opt = None;

        // 如果是 UDP 协议，绑定 UDP 套接字并启动 UDP 读取任务。
        if protocol == PackageProtocol::UDP {
            if let Ok(udp_socket) = UdpSocket::bind("0.0.0.0:0").await {
                let arc_udp_socket = Arc::new(udp_socket);
                let arc_udp_socket_clone = arc_udp_socket.clone();
                udp_socket_opt = Some(arc_udp_socket.clone());

                // 启动一个异步任务来处理接收到的 UDP 包。
                udp_read_job_opt = Some(spawn(async move {
                    let uuid_byte = uuid_clone.as_bytes();
                    let mut buf = [0u8; 2048];
                    while let Ok((n, source_addr)) = arc_udp_socket_clone.recv_from(&mut buf).await {
                        if n == 0 {
                            break;
                        }
                        *active_time_clone.write().await = Instant::now();
                        let mut data = vec![];
                        data.append(&mut uuid_byte.to_vec());
                        data.append(&mut buf[..n].to_vec());
                        let tunnel_package = TunnelPackage::new(
                            PackageCmd::CTdata,
                            PackageProtocol::UDP,
                            Some(source_addr_clone.clone()),
                            Some(source_addr_clone.clone()),
                            Some(data),
                        );
                        if tunnel_clone.write_to_tunnel(tunnel_package).await.is_err() {
                            break;
                        }
                    }
                }));
            }
        }

        Arc::new(Route {
            protocol,
            source_addr,
            target_addr,
            tunnel: RwLock::new(Some(tunnel)),
            route_table,
            tcp_read_job: RwLock::new(None),
            tcp_write_job: Arc::new(RwLock::new(None)),
            tcp_writer: RwLock::new(None),
            udp_read_job: udp_read_job_opt,
            udp_socket: RwLock::new(udp_socket_opt),
            active_time,
            uuid,
        })
    }
}
